# 基础
tickTime=2000
initLimit=10
syncLimit=5
dataDir=/export/data/zookeeper
dataLogDir=/export/logs/zookeeper
clientPort=4181
maxClientCnxns=300
autopurge.snapRetainCount=3
autopurge.purgeInterval=24

# 集群
server.1=192.168.200.101:2888:3888
server.2=192.168.200.102:2888:3888
server.3=192.168.200.103:2888:3888

# ---------- SASL ----------
authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider
requireClientAuthScheme=sasl
jaasLoginRenew=3600000
sessionRequireClientSASLAuth=true
# ZK 节点间 SASL
quorum.auth.enableSasl=true
quorum.auth.learnerRequireSasl=true
quorum.auth.serverRequireSasl=true
quorum.auth.learner.loginContext=QuorumLearner
quorum.auth.server.loginContext=QuorumServer
quorum.cnxn.threads.size=10