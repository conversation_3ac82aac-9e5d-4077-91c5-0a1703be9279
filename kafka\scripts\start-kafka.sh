#!/bin/bash

# Kafka Cluster Start Script

# Source environment
source /export/server/kafka/config/kafka-env.sh

# Create directories
mkdir -p /export/data/kafka-logs
mkdir -p /export/logs/kafka
chown -R kafka:kafka /export/data/kafka-logs
chown -R kafka:kafka /export/logs/kafka

# Get current host IP
HOST_IP=$(hostname -I | awk "{print \$1}")

# Determine server config based on IP
case $HOST_IP in
    "***************")
        CONFIG_FILE="/export/server/kafka/config/server-101.properties"
        ;;
    "***************")
        CONFIG_FILE="/export/server/kafka/config/server-102.properties"
        ;;
    "***************")
        CONFIG_FILE="/export/server/kafka/config/server-103.properties"
        ;;
    *)
        echo "Unknown host IP: $HOST_IP"
        exit 1
        ;;
esac

echo "Starting Kafka on $HOST_IP with config: $CONFIG_FILE"

# Start Kafka
nohup $KAFKA_HOME/bin/kafka-server-start.sh $CONFIG_FILE > /export/logs/kafka/kafka.out 2>&1 &

echo "Kafka started. Check logs at /export/logs/kafka/"
