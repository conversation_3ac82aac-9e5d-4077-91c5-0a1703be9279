#!/bin/bash

# Kafka Environment Configuration

# Java Home
export JAVA_HOME=/export/tools/jdk17

# Kafka Home
export KAFKA_HOME=/export/server/kafka

# Kafka Heap Settings
export KAFKA_HEAP_OPTS="-Xmx2G -Xms2G"

# Kafka JVM Performance Settings
export KAFKA_JVM_PERFORMANCE_OPTS="-server -XX:+UseG1GC -XX:MaxGCPauseMillis=20 -XX:InitiatingHeapOccupancyPercent=35 -XX:+ExplicitGCInvokesConcurrent -Djava.awt.headless=true"

# Kafka Log Directory
export KAFKA_LOG_DIR=/export/logs/kafka

# Kafka Data Directory
export KAFKA_DATA_DIR=/export/data

# Kafka User
export KAFKA_USER=kafka

# JMX Settings
export JMX_PORT=9999
export KAFKA_JMX_OPTS="-Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.port=$JMX_PORT"

# GC Log Settings
export KAFKA_GC_LOG_OPTS="-Xloggc:$KAFKA_LOG_DIR/kafkaServer-gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M"

# Other JVM Settings
export KAFKA_OPTS="-Djava.security.auth.login.config=$KAFKA_HOME/config/kafka_server_jaas.conf"
