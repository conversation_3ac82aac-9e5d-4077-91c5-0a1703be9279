# ZooKeeper 3.8.4 SASL集群部署说明

## 1. 部署前准备

### 1.1 创建必要目录
在每台服务器上执行：
```bash
# 创建数据目录
sudo mkdir -p /export/data/zookeeper
sudo mkdir -p /export/logs/zookeeper

# 设置权限
sudo chown -R appuser:appuser /export/data/zookeeper
sudo chown -R appuser:appuser /export/logs/zookeeper
sudo chmod 755 /export/data/zookeeper
sudo chmod 755 /export/logs/zookeeper
```

### 1.2 创建myid文件
在各节点的数据目录下创建myid文件：

**192.168.200.101节点：**
```bash
echo "1" > /export/data/zookeeper/myid
```

**192.168.200.102节点：**
```bash
echo "2" > /export/data/zookeeper/myid
```

*****************节点：**
```bash
echo "3" > /export/data/zookeeper/myid
```

## 2. 配置文件部署

将以下配置文件复制到ZooKeeper的conf目录（/export/server/zookeeper/conf）：
- `zoo.cfg` - 主配置文件
- `jaas.conf` - SASL认证配置
- `zkEnv.sh` - 环境变量配置
- `log4j.properties` - 日志配置

## 3. 启动集群

### 3.1 启动脚本
**重要：首先创建myid文件**
```bash
# 运行myid创建脚本（会自动检测IP并创建对应的myid）
chmod +x create-myid.sh
./create-myid.sh

# 或者手动创建（在各自节点上执行）：
# 节点1: echo "1" > /export/data/zookeeper/myid
# 节点2: echo "2" > /export/data/zookeeper/myid
# 节点3: echo "3" > /export/data/zookeeper/myid
```

创建启动脚本 `start-zk.sh`：
```bash
#!/bin/bash
source /export/server/zookeeper/conf/zkEnv.sh
cd $ZOOKEEPER_HOME
bin/zkServer.sh start
```

### 3.2 停止脚本
创建停止脚本 `stop-zk.sh`：
```bash
#!/bin/bash
source /export/server/zookeeper/conf/zkEnv.sh
cd $ZOOKEEPER_HOME
bin/zkServer.sh stop
```

### 3.3 状态检查脚本
创建状态检查脚本 `status-zk.sh`：
```bash
#!/bin/bash
source /export/server/zookeeper/conf/zkEnv.sh
cd $ZOOKEEPER_HOME
bin/zkServer.sh status
```

## 4. 客户端连接示例

### 4.1 使用zkCli.sh连接
```bash
# 需要先配置客户端的JAAS配置
export CLIENT_JVMFLAGS="-Djava.security.auth.login.config=/opt/zookeeper/conf/jaas.conf"
bin/zkCli.sh -server 192.168.200.101:4181,192.168.200.102:4181,***************:4181
```

### 4.2 Java客户端连接示例
```java
System.setProperty("java.security.auth.login.config", "/path/to/jaas.conf");
String connectString = "192.168.200.101:4181,192.168.200.102:4181,***************:4181";
ZooKeeper zk = new ZooKeeper(connectString, 3000, null);
```

## 5. 监控和维护

### 5.1 检查集群状态
```bash
# 检查各节点状态
echo stat | nc 192.168.200.101 4181
echo stat | nc 192.168.200.102 4181  
echo stat | nc *************** 4181

# 检查leader
echo srvr | nc 192.168.200.101 4181
```

### 5.2 日志位置
- 应用日志：`/export/logs/zookeeper/zookeeper.log`
- 跟踪日志：`/export/logs/zookeeper/zookeeper_trace.log`
- 数据快照：`/export/data/zookeeper/version-2/`

## 6. 安全注意事项

1. **密码安全**：修改jaas.conf中的默认密码
2. **网络安全**：配置防火墙规则，只允许必要的端口访问
3. **文件权限**：确保配置文件权限设置正确
4. **定期备份**：定期备份数据目录和配置文件

## 7. 故障排查

### 7.1 常见问题
- 检查myid文件是否正确创建
- 检查网络连通性（2888、3888、4181端口）
- 检查SASL认证配置是否正确
- 检查日志文件中的错误信息

### 7.2 调试命令
```bash
# 查看详细启动日志
tail -f /export/logs/zookeeper/zookeeper.log

# 检查端口监听
netstat -tlnp | grep -E "(2888|3888|4181)"

# 测试SASL认证
echo ruok | nc 192.168.200.101 4181
```
