#!/bin/bash

# Kafka Cluster Status Check Script

# Source environment
source /export/server/kafka/config/kafka-env.sh

echo "=== Kafka Cluster Status ==="

# Check if Kafka process is running
KAFKA_PID=$(ps aux | grep kafka.Kafka | grep -v grep | awk "{print \$2}")

if [ -n "$KAFKA_PID" ]; then
    echo " Kafka is running (PID: $KAFKA_PID)"
else
    echo " Kafka is not running"
    exit 1
fi

# Check Kafka broker registration in ZooKeeper
echo ""
echo "=== Broker Registration Check ==="
$KAFKA_HOME/bin/kafka-broker-api-versions.sh --bootstrap-server localhost:9092

# List topics
echo ""
echo "=== Topics List ==="
$KAFKA_HOME/bin/kafka-topics.sh --bootstrap-server localhost:9092 --list

# Check cluster metadata
echo ""
echo "=== Cluster Metadata ==="
$KAFKA_HOME/bin/kafka-metadata-shell.sh --snapshot /export/data/kafka-logs/__cluster_metadata-0/00000000000000000000.log --print

echo ""
echo "=== Log Directory Status ==="
ls -la /export/data/kafka-logs/

echo ""
echo "=== Recent Log Entries ==="
tail -20 /export/logs/kafka/server.log
