#!/bin/bash

# Kafka Cluster Deployment Script

HOSTS=("***************" "***************" "***************")
KAFKA_USER="root"  
KAFKA_HOME="/export/server/kafka"
LOCAL_CONFIG_DIR="./conf"
LOCAL_SCRIPTS_DIR="./scripts"

echo "=== Kafka Cluster Deployment Script ==="

# Function to deploy to a single host
deploy_to_host() {
    local host=$1
    local broker_id=$2
    
    echo "Deploying to $host (broker.id=$broker_id)..."
    
    # Create directories
    ssh $KAFKA_USER@$host "mkdir -p $KAFKA_HOME/config $KAFKA_HOME/scripts /export/data/kafka-logs /export/logs/kafka"
    
    # Copy configuration files
    scp $LOCAL_CONFIG_DIR/server-${broker_id}.properties $KAFKA_USER@$host:$KAFKA_HOME/config/server.properties
    scp $LOCAL_CONFIG_DIR/kafka-env.sh $KAFKA_USER@$host:$KAFKA_HOME/config/
    scp $LOCAL_CONFIG_DIR/log4j.properties $KAFKA_USER@$host:$KAFKA_HOME/config/
    
    # Copy scripts
    scp $LOCAL_SCRIPTS_DIR/start-kafka.sh $KAFKA_USER@$host:$KAFKA_HOME/scripts/
    scp $LOCAL_SCRIPTS_DIR/stop-kafka.sh $KAFKA_USER@$host:$KAFKA_HOME/scripts/
    scp $LOCAL_SCRIPTS_DIR/status-kafka.sh $KAFKA_USER@$host:$KAFKA_HOME/scripts/
    
    # Set permissions
    ssh $KAFKA_USER@$host "chmod +x $KAFKA_HOME/scripts/*.sh"
    ssh $KAFKA_USER@$host "chown -R $KAFKA_USER:$KAFKA_USER $KAFKA_HOME /export/data/kafka-logs /export/logs/kafka"
    
    echo "✓ Deployment to $host completed"
}

# Deploy to all hosts
for i in "${!HOSTS[@]}"; do
    host=${HOSTS[$i]}
    broker_id=$((i + 101))
    deploy_to_host $host $broker_id
done

echo ""
echo "=== Deployment Summary ==="
echo "Kafka configuration deployed to:"
for i in "${!HOSTS[@]}"; do
    host=${HOSTS[$i]}
    broker_id=$((i + 101))
    echo "  - $host (broker.id=$broker_id)"
done

echo ""
echo "=== Next Steps ==="
echo "1. Ensure ZooKeeper is running on all nodes"
echo "2. Start Kafka cluster: ./cluster-kafka.sh start"
echo "3. Check status: ./cluster-kafka.sh status"
echo ""
echo "Individual node operations:"
echo "  - Start: ssh kafka@HOST '/export/server/kafka/scripts/start-kafka.sh'"
echo "  - Stop:  ssh kafka@HOST '/export/server/kafka/scripts/stop-kafka.sh'"
echo "  - Status: ssh kafka@HOST '/export/server/kafka/scripts/status-kafka.sh'"
