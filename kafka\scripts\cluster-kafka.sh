#!/bin/bash

# Kafka Cluster Management Script

HOSTS=("***************" "***************" "***************")
KAFKA_USER="root"

case $1 in
    "start")
        echo "Starting Kafka cluster..."
        for host in "${HOSTS[@]}"; do
            echo "Starting Kafka on $host"
            ssh $KAFKA_USER@$host "/export/server/kafka/scripts/start-kafka.sh"
        done
        ;;
    "stop")
        echo "Stopping Kafka cluster..."
        for host in "${HOSTS[@]}"; do
            echo "Stopping Kafka on $host"
            ssh $KAFKA_USER@$host "/export/server/kafka/scripts/stop-kafka.sh"
        done
        ;;
    "status")
        echo "Checking Kafka cluster status..."
        for host in "${HOSTS[@]}"; do
            echo "=== Status on $host ==="
            ssh $KAFKA_USER@$host "/export/server/kafka/scripts/status-kafka.sh"
            echo ""
        done
        ;;
    "restart")
        echo "Restarting Kafka cluster..."
        $0 stop
        sleep 10
        $0 start
        ;;
    *)
        echo "Usage: $0 {start|stop|status|restart}"
        exit 1
        ;;
esac
