# Kafka Server Configuration for 192.168.200.101
broker.id=1
listeners=PLAINTEXT://192.168.200.101:9092
advertised.listeners=PLAINTEXT://192.168.200.101:9092
listener.security.protocol.map=PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL

num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600

log.dirs=/export/data/kafka-logs
num.partitions=3
num.recovery.threads.per.data.dir=1

offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2

log.retention.hours=168
log.retention.bytes=1073741824
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000

zookeeper.connect=192.168.200.101:2181,192.168.200.102:2181,192.168.200.103:2181/kafka
zookeeper.connection.timeout.ms=18000

group.initial.rebalance.delay.ms=0
num.replica.fetchers=4
replica.lag.time.max.ms=30000
replica.fetch.max.bytes=1048576
compression.type=lz4

auto.create.topics.enable=true
default.replication.factor=3
min.insync.replicas=2
delete.topic.enable=true
message.max.bytes=1000000
max.request.size=1048576
